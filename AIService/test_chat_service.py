#!/usr/bin/env python3
"""
Test script to verify the chat service RAG chain functionality.
This script tests both streaming and non-streaming responses to ensure context is being passed correctly.
"""

import asyncio
import sys
import os
from loguru import logger

# Add the AIService directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.proposal.chat_service import ChatService

async def test_chat_service():
    """Test the chat service with a sample question."""
    
    # Configure logger to show debug messages
    logger.remove()
    logger.add(sys.stderr, level="DEBUG")
    
    logger.info("Starting chat service test...")
    
    # Initialize chat service
    chat_service = ChatService()
    
    # Test parameters - you may need to adjust these based on your actual data
    test_question = "What are the key requirements for this opportunity?"
    test_opportunity_id = "test_opportunity"  # Replace with actual opportunity ID
    test_tenant_id = "test_tenant"  # Replace with actual tenant ID
    test_source = "custom"  # or "standard"
    
    logger.info(f"Testing with question: '{test_question}'")
    logger.info(f"Opportunity ID: {test_opportunity_id}")
    logger.info(f"Tenant ID: {test_tenant_id}")
    logger.info(f"Source: {test_source}")
    
    try:
        # Test non-streaming response
        logger.info("\n=== Testing Non-Streaming Response ===")
        non_streaming_result = await chat_service.get_answer(
            question=test_question,
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            streaming=False,
            max_chunks=3
        )
        
        logger.info(f"Non-streaming result type: {type(non_streaming_result)}")
        logger.info(f"Non-streaming result: {non_streaming_result}")
        
        # Test streaming response
        logger.info("\n=== Testing Streaming Response ===")
        streaming_result = await chat_service.get_answer(
            question=test_question,
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            streaming=True,
            max_chunks=3
        )
        
        logger.info(f"Streaming result type: {type(streaming_result)}")
        
        # Collect streaming chunks
        streaming_content = ""
        chunk_count = 0
        async for chunk in streaming_result:
            chunk_count += 1
            content = getattr(chunk, "content", str(chunk))
            streaming_content += content
            logger.debug(f"Chunk {chunk_count}: {content}")
            
            # Limit chunks for testing
            if chunk_count > 10:
                break
        
        logger.info(f"Streaming content (first 500 chars): {streaming_content[:500]}")
        logger.info(f"Total chunks received: {chunk_count}")
        
        # Test validation
        logger.info("\n=== Testing Question Validation ===")
        valid_question = ChatService.validate_question(test_question)
        invalid_question = ChatService.validate_question("")
        logger.info(f"Valid question test: {valid_question}")
        logger.info(f"Invalid question test: {invalid_question}")
        
        logger.info("\n=== Chat Service Test Completed Successfully ===")
        
    except Exception as e:
        logger.error(f"Error during chat service test: {e}")
        logger.exception("Full exception details:")
        return False
    
    return True

async def test_context_retrieval():
    """Test just the context retrieval part to see if ChromaDB is working."""
    
    logger.info("\n=== Testing Context Retrieval Only ===")
    
    chat_service = ChatService()
    
    test_question = "What are the requirements?"
    test_opportunity_id = "test_opportunity"
    test_tenant_id = "test_tenant"
    test_source = "custom"
    
    try:
        # Test the _get_relevant_chunks method directly
        relevant_chunks = await chat_service._get_relevant_chunks(
            question=test_question,
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            max_chunks=3
        )
        
        logger.info(f"Retrieved {len(relevant_chunks)} chunks")
        for i, chunk in enumerate(relevant_chunks):
            logger.info(f"Chunk {i+1} (first 200 chars): {chunk[:200]}...")
            
        return len(relevant_chunks) > 0
        
    except Exception as e:
        logger.error(f"Error during context retrieval test: {e}")
        logger.exception("Full exception details:")
        return False

if __name__ == "__main__":
    async def main():
        logger.info("Starting comprehensive chat service tests...")
        
        # Test context retrieval first
        context_success = await test_context_retrieval()
        logger.info(f"Context retrieval test: {'PASSED' if context_success else 'FAILED'}")
        
        # Test full chat service
        chat_success = await test_chat_service()
        logger.info(f"Chat service test: {'PASSED' if chat_success else 'FAILED'}")
        
        if context_success and chat_success:
            logger.info("All tests PASSED!")
        else:
            logger.error("Some tests FAILED!")
            
    asyncio.run(main())
