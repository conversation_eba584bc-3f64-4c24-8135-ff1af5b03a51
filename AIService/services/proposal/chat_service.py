import asyncio
import json
from typing import Any, Dict, Iterator, List, Optional, AsyncGenerator

from langchain_core.messages import BaseMessageChunk, BaseMessage
from langchain.schema import Document
from services.chroma.chroma_service import ChromaService
from database import get_kontratar_db
from langchain_ollama import ChatOllama
from loguru import logger

class ChatService:
    """
    Service for question answering using ChromaDB and LLM with streaming and non-streaming options.
    """
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.chroma_service = ChromaService(embedding_api_url, None)
        #self.llm = KontratarLLM(api_url=llm_api_url, api_key=None)
        self.llm = ChatOllama(model="gemma3:27b", temperature=0.0, base_url=llm_api_url)

    async def _get_relevant_chunks(
        self,
        question: str,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        max_chunks: int
    ) -> List[str]:
        """Get relevant chunks using existing ChromaService."""
        async for db in get_kontratar_db():
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            logger.info(f"Using collection_name: '{collection_name}' for ChromaDB retrieval")

            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, question, n_results=max_chunks
            )

            logger.info(f"Retrieved {len(relevant_chunks)} chunks from ChromaDB for Document Chain")

            # Debug logging to show actual context content
            if relevant_chunks:
                logger.debug(f"Context chunks preview:")
                for i, chunk in enumerate(relevant_chunks[:2]):  # Show first 2 chunks
                    preview = chunk[:200] + "..." if len(chunk) > 200 else chunk
                    logger.debug(f"  Chunk {i+1}: {preview}")
            else:
                logger.warning("No relevant chunks retrieved from ChromaDB - LLM will respond without context")

            return relevant_chunks

        # If no database connection, return empty list
        return []

    def _create_context_prompt(self, context_docs: List[Document], query: str) -> str:
        """Create a formatted prompt with context and query."""

        # Format the context documents
        context_text = "\n\n".join([doc.page_content for doc in context_docs])

        system_prompt = f"""Role: Government Solicitation Expert Assistant

Task: You will be given a question to answer about a government opportunity. YOU MUST answer the question using ONLY the provided context.

Rules:
1. **Answer Format**: Provide clear, concise, and accurate answers based on the context
2. **Source Attribution**: Base your answers primarily on the provided context
3. **Professional Tone**: Use professional and formal language appropriate for government documents
4. **Accuracy**: If the context doesn't contain enough information to answer the question, say so
5. **Structure**: Organize your response logically with clear sections if needed

CONTEXT:
{context_text}

QUESTION: {query}

Please answer the question based on the context provided above."""

        return system_prompt


    
    async def get_answer(
        self,
        question: str,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        streaming: bool = False,
        max_chunks: int = 3,
    ) -> Iterator[BaseMessageChunk] | str:
        """
        Get an answer to a question using ChromaDB and LLM.

        Args:
            question: The question to answer
            opportunity_id: The opportunity ID
            tenant_id: The tenant ID
            source: The source type ("custom" or other)
            max_chunks: Maximum number of relevant chunks to retrieve
            streaming: Whether to return streaming response or not

        Returns:
            Dict with 'answer' and 'context' keys
        """

        # Add logs for tracing execution and debugging
        logger.info(f"Received question: '{question}' for opportunity_id: '{opportunity_id}', tenant_id: '{tenant_id}', source: '{source}', streaming: {streaming}, max_chunks: {max_chunks}")

        # Get relevant chunks using existing ChromaService
        relevant_chunks = await self._get_relevant_chunks(
            question, opportunity_id, tenant_id, source, max_chunks
        )

        # Convert chunks to LangChain Documents
        documents = [Document(page_content=chunk) for chunk in relevant_chunks]

        logger.info(f"Retrieved {len(documents)} documents for context")

        if streaming:
            logger.info("Requesting streaming response from LLM")
            result = await self._get_streaming_response(question, documents)
            logger.info("Streaming response initiated")
            return result
        else:
            logger.info("Requesting non-streaming response from LLM")
            result = await self._get_non_streaming_response(question, documents)
            logger.info("Non-streaming response received")
            return result

    async def _get_non_streaming_response(
        self,
        query: str,
        context_docs: List[Document] = []
    ) -> str:
        """
        Get a non-streaming response from the LLM using context.
        """
        logger.info("Invoking LLM for non-streaming response with context")
        logger.info(f"Using {len(context_docs)} documents for context in non-streaming response")

        if context_docs:
            logger.debug(f"Context documents being passed to LLM:")
            for i, doc in enumerate(context_docs[:2]):
                preview = doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content
                logger.debug(f"  Document {i+1}: {preview}")
        else:
            logger.warning("No context documents available for LLM")

        # Create the formatted prompt with context
        formatted_prompt = self._create_context_prompt(context_docs, query)

        messages = [("human", formatted_prompt)]
        result = await self.llm.ainvoke(messages)

        logger.info("LLM execution complete for non-streaming response")
        logger.debug(f"LLM response content: {str(result.content)[:500]}{'...' if len(str(result.content)) > 500 else ''}")
        return str(result.content)

    async def _get_streaming_response(
        self,
        query: str,
        context_docs: List[Document] = []
    ) -> Iterator[Any]:
        """
        Get a streaming response from the LLM.
        """
        logger.info("Invoking LLM for streaming response")
        logger.info(f"{len(context_docs)} documents created from relevant chunks for streaming")

        if context_docs:
            logger.debug(f"Context documents being passed to streaming LLM:")
            for i, doc in enumerate(context_docs[:2]):
                preview = doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content
                logger.debug(f"  Document {i+1}: {preview}")
        else:
            logger.warning("No context documents available for streaming LLM")

        formatted_prompt = self._create_context_prompt(context_docs, query)

        messages = [("human", formatted_prompt)]
        return self.llm.astream(messages)

    @staticmethod
    def validate_question(question: str) -> bool:
        """
        Validate if the question is appropriate for the chat service.
        """
        if not question or not question.strip():
            return False
        
        # Add any additional validation rules here
        # For example, check for minimum length, inappropriate content, etc.
        
        return True