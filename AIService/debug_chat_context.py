#!/usr/bin/env python3
"""
Debug script to test ChromaDB context retrieval for the specific request.
"""

import asyncio
import sys
import os
from loguru import logger

# Add the AIService directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.proposal.chat_service import ChatService

async def debug_context_retrieval():
    """Debug the exact request that's failing."""
    
    # Configure logger to show debug messages
    logger.remove()
    logger.add(sys.stderr, level="DEBUG")
    
    logger.info("Starting debug for specific request...")
    
    # Initialize chat service
    chat_service = ChatService()
    
    # Use the exact parameters from your curl request
    question = "what are the available Key Personnel Position"
    opportunity_id = "iRiYNgd8RC"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    source = "custom"
    max_chunks = 3
    
    logger.info(f"Testing with exact parameters:")
    logger.info(f"  Question: '{question}'")
    logger.info(f"  Opportunity ID: {opportunity_id}")
    logger.info(f"  Tenant ID: {tenant_id}")
    logger.info(f"  Source: {source}")
    logger.info(f"  Max chunks: {max_chunks}")
    
    try:
        # Test the _get_relevant_chunks method directly
        logger.info("\n=== Testing ChromaDB Context Retrieval ===")
        relevant_chunks = await chat_service._get_relevant_chunks(
            question=question,
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            max_chunks=max_chunks
        )
        
        logger.info(f"\n=== RESULTS ===")
        logger.info(f"Retrieved {len(relevant_chunks)} chunks from ChromaDB")
        
        if relevant_chunks:
            logger.info("Context chunks found:")
            for i, chunk in enumerate(relevant_chunks):
                logger.info(f"\n--- Chunk {i+1} ---")
                logger.info(f"Length: {len(chunk)} characters")
                logger.info(f"Content preview (first 300 chars):")
                logger.info(chunk[:300] + "..." if len(chunk) > 300 else chunk)
        else:
            logger.warning("❌ NO CONTEXT CHUNKS RETRIEVED!")
            logger.warning("This explains why the LLM is giving generic responses.")
            
            # Let's check what collection name is being used
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            logger.warning(f"Collection name being used: '{collection_name}'")
            
        # Now test the full chat service
        logger.info("\n=== Testing Full Chat Service ===")
        result = await chat_service.get_answer(
            question=question,
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            streaming=False,
            max_chunks=max_chunks
        )
        
        logger.info(f"\n=== CHAT SERVICE RESULT ===")
        logger.info(f"Result type: {type(result)}")
        logger.info(f"Result length: {len(str(result))} characters")
        logger.info(f"Result preview (first 500 chars):")
        logger.info(str(result)[:500] + "..." if len(str(result)) > 500 else str(result))
        
        # Check if the result contains context-specific information
        if "government" in str(result).lower() or "solicitation" in str(result).lower() or "rfp" in str(result).lower():
            logger.info("✅ Result appears to contain government/solicitation context")
        else:
            logger.warning("❌ Result appears to be generic - no specific context used")
            
    except Exception as e:
        logger.error(f"Error during debug: {e}")
        logger.exception("Full exception details:")

if __name__ == "__main__":
    asyncio.run(debug_context_retrieval())
